/**
 * API Endpoints for the application
 */

const APIEndPoints = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/user/login',
    REGISTER: '/auth/register',
    REFRESH_TOKEN: '/auth/refresh-token',
    FORGOT_PASSWORD: '/user/resetPassword',
    RESET_PASSWORD: '/user/updatePassword',
    VERIFY_RESET_PASSWORD: '/user/verifyResetPasswordCode',
  },

  // User endpoints
  USER: {
    PROFILE: '/user/profile',
    UPDATE_PROFILE: '/user/profile',
    CHANGE_PASSWORD: '/user/change-password',
  },

  // Content endpoints
  CONTENT: {
    VENUES: '/content/venues',
    VENUE_DETAILS: (id: string) => `/content/venues/${id}`,
    ALBUMS: '/dashboard/getalbums',
    EVENT_DETAILS: (id: string) => `/content/events/${id}`,
  },

  // Venue endpoints
  VENUE: {
    BAR_LIST: '/v2/bar/getBars',
    // VENUE_DETAILS: (id: string) => `/content/venues/${id}`,
  },
};

export default APIEndPoints;
