/**
 * Type definitions for network requests and responses
 */

export interface RequestConfig {
  headers?: Record<string, string>;
  params?: Record<string, any>;
  timeout?: number;
}

export interface NetworkResponse<T = any> {
  data: T;
  status: number;
  headers: Record<string, string>;
}

export interface NetworkError {
  message: string;
  status?: number;
  data?: any;
}

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

export interface RequestOptions {
  method: HttpMethod;
  url: string;
  data?: any;
  config?: RequestConfig;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}

/**
 * File upload related types
 */
export interface FileObject {
  uri: string;
  name: string;
  type: string;
}

export interface MultipartFormData {
  name: string;
  value: string | FileObject;
}

export interface FileUploadConfig extends RequestConfig {
  onUploadProgress?: (progressEvent: any) => void;
}
