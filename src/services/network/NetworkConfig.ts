/**
 * Network configuration for the application
 */

export enum Environment {
  DEV = 'dev',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

interface EnvironmentConfig {
  baseURL: string;
  timeout: number;
  apiKey?: string;
}

const ENV_CONFIG: Record<Environment, EnvironmentConfig> = {
  [Environment.DEV]: {
    baseURL: 'https://app-dev-api.maanmandir.org',
    timeout: 30000,
  },
  [Environment.STAGING]: {
    baseURL: 'https://stage-cms.mytabinfo.com/api',
    timeout: 15000,
  },
  [Environment.PRODUCTION]: {
    baseURL: 'https://cms.mytabinfo.com/api',
    timeout: 10000,
  },
};

// Set the current environment
const CURRENT_ENV = Environment.STAGING;

export const NetworkConfig = {
  ...ENV_CONFIG[CURRENT_ENV],
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
};

export default NetworkConfig;
