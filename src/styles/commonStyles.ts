import {StyleSheet} from 'react-native';
import Colors from '../utils/colors';
import {FontFamily, FontSize} from '../utils/fonts';

// This file now only contains global/shared styles. Component-specific styles have been moved to their respective folders.

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // paddingHorizontal: 24,
    justifyContent: 'center',
  },
  heading: {
    fontSize: FontSize.size_24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  input: {
    height: 48,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  gradient: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  headerViewStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.app_white,
    padding: 10,
    fontFamily: FontFamily.Balloobhai2_BOLD,
    color: Colors.app_white,
  },
  headerImageStyle: {
    height: 40,
    marginBottom: 15,
    marginTop: 5,
  },

  customSafeAreaViewStyle: {
    flex: 1,
    backgroundColor: Colors.app_white,
  },
});

export default styles;
