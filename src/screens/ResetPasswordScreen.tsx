import {useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '../navigation/AppNavigator';
import {TopSafeAreaView} from '../components/CustomSafeAreaView';
import HeaderView from '../components/HeaderView';
import {
  AlertMessages,
  AssetImages,
  ButtonTitle,
  Placeholders,
  StaticContents,
} from '../utils/constants';
import GradientView from '../components/GradientView';
import TitleDescriptionView from '../components/TitleDescriptionView';
import ContainerInputFieldsView from '../components/Login/LoginInputFieldsView';
import InputField from '../components/InputField';
import TextButton from '../components/TextButton';
import TextButtonStyle from '../components/TextButton/styles';
import {AuthService} from '../services/api';
import {handleApiError} from '../utils/apiUtils';

type Props = NativeStackScreenProps<RootStackParamList, 'ResetPassword'>;

const ResetPasswordScreen: React.FC<Props> = ({route, navigation}) => {
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const {id, resetPasswordCode} = route.params;

  const handleResetPassword = async () => {
    if (!password.trim()) {
      setPasswordError(AlertMessages.error.passwordRequired);
      return;
    }
    if (password.length < 6) {
      setPasswordError(AlertMessages.error.shortPassword);
      return;
    }
    navigation.popToTop();
    //await resetPasswordWSCall();
  };

  const resetPasswordWSCall = async () => {
    const requestParam = {
      resetPasswordCode: resetPasswordCode.toString(),
      password: password.trim(),
      id: id,
    };
    console.log('requestParam', requestParam);
    try {
      const response = await AuthService.resetPassword(requestParam);

      if (response.success && response.data) {
        navigation.navigate('Login');
      } else {
        console.log('response', response.message);
        setPasswordError(response.message);
      }
    } catch (error) {
      console.log('response', error);
      setPasswordError(handleApiError(error));
    }
  };

  return (
    <>
      <TopSafeAreaView />
      <HeaderView
        leftIcon={AssetImages.ic_back_icon}
        onPress={() => navigation.goBack()}
      />
      <GradientView>
        <TitleDescriptionView
          heading={StaticContents.ResetPasswordScreen.resetPassTitle}
          subheading={StaticContents.ResetPasswordScreen.resetPassSubtitle}
        />
        <ContainerInputFieldsView>
          <InputField
            placeholder={Placeholders.password}
            value={password}
            onChangeText={setPassword}
            secureTextEntry={true}
            error={passwordError ? {message: passwordError} : null}
          />
          <TextButton
            text={ButtonTitle.saveNewpass}
            onPress={() => handleResetPassword()}
            textButtonStyle={TextButtonStyle.btnStyle}
          />
        </ContainerInputFieldsView>
      </GradientView>
    </>
  );
};
export default ResetPasswordScreen;
