import React, {useState} from 'react';
import {
  Alert,
  StatusBar,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {TopSafeAreaView} from '../components/CustomSafeAreaView/index';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '../navigation/AppNavigator';
import InputField from '../components/InputField/index';
import GradientView from '../components/GradientView/index';
import HeaderView from '../components/HeaderView/index';
import TitleDescriptionView from '../components/TitleDescriptionView/index';
import {
  AlertMessages,
  AlertTitles,
  StaticContents,
  Placeholders,
  ButtonTitle,
} from '../utils/constants';
import {FontFamily, FontSize} from '../utils/fonts';
import ContainerInputFieldsView from '../components/Login/LoginInputFieldsView/index';
import TextButton from '../components/TextButton/index';
import TextButtonStyle from '../components/TextButton/styles';
import NoAccountView from '../components/Login/NoAccountView/index';
import {AuthService} from '../services/api';
import {handleApiError} from '../utils/apiUtils';
import Colors from '../utils/colors';
import {useAppDispatch} from '../redux/store/hooks';
import {loginUser} from '../redux/store/slices/userSlice';

type Props = NativeStackScreenProps<RootStackParamList, 'Login'>;

const LoginScreen: React.FC<Props> = ({navigation}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const dispatch = useAppDispatch();

  const validateAndLogin = async () => {
    // Reset error states
    setEmailError('');
    setPasswordError('');

    // Validate inputs
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!email.trim()) {
      setEmailError(AlertMessages.error.emailRequired);
      return;
    }

    if (!password.trim()) {
      setPasswordError(AlertMessages.error.passwordRequired);
      return;
    }

    if (!emailRegex.test(email)) {
      setEmailError(AlertMessages.error.invalidEmail);
      return;
    }

    if (password.length < 6) {
      setPasswordError(AlertMessages.error.shortPassword);
      return;
    }

    // All validations passed, proceed with login
    try {
      // Prepare login request
      const loginRequest = {
        email: email.trim(),
        password: password.trim(),
        loginType: 'NORMAL', // Assuming email login type
        deviceType: 'ios', // 'ios' or 'android'
        deviceToken: 'dummy-token', // In a real app, you would use a real device token for push notifications
      };

      // Call the login API
      const response = await AuthService.login(loginRequest);
      console.log(
        '🚀 ~ validateAndLogin ~ response:',
        JSON.stringify(response, null, 2),
      );

      // Handle the response
      if (response.success && response.data) {
        // Dispatch the user data to Redux
        dispatch(loginUser(response.data));

        // Navigate to Home screen
        navigation.navigate('Home');
      } else {
        // Show error message from API
        Alert.alert(AlertTitles.error, response.message);
      }
    } catch (error) {
      // Handle API error
      Alert.alert(AlertTitles.error, handleApiError(error));
    }
  };

  const toggleRememberMe = () => {
    setRememberMe(!rememberMe);
  };

  return (
    <>
      <TopSafeAreaView />
      <HeaderView />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{flex: 1}}>
        <ScrollView contentContainerStyle={{flexGrow: 1}}>
          <GradientView>
            <TitleDescriptionView
              heading={StaticContents.loginScreen.loginTitle}
              subheading={StaticContents.loginScreen.loginSubtitle}
            />

            <ContainerInputFieldsView>
              <InputField
                placeholder={Placeholders.email || 'Email'}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                error={emailError ? {message: emailError} : null}
              />
              <InputField
                placeholder={Placeholders.password || 'Password'}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={true}
                error={passwordError ? {message: passwordError} : null}
              />
              <View style={TextButtonStyle.container}>
                <TextButton
                  onPress={toggleRememberMe}
                  text={ButtonTitle.rememberMe}
                  textButtonStyle={{
                    ...TextButtonStyle.forgotPassBtnStyle,
                    color: rememberMe
                      ? Colors.app_orange
                      : TextButtonStyle.forgotPassBtnStyle.color,
                  }}
                />
                <TextButton
                  onPress={() => navigation.navigate('ForgotPassword')}
                  text={ButtonTitle.forgotPassword}
                  textButtonStyle={TextButtonStyle.forgotPassBtnStyle}
                />
              </View>
              <TextButton
                text={ButtonTitle.login}
                onPress={validateAndLogin}
                textButtonStyle={TextButtonStyle.btnStyle}
              />
              <NoAccountView
                onClick={() => navigation.navigate('Home')}
                noMoreAccountFontFamily={FontFamily.Balloobhai2_REGULAR}
                signUpFontfamily={FontFamily.Balloobhai2_BOLD}
              />
            </ContainerInputFieldsView>
          </GradientView>
        </ScrollView>
      </KeyboardAvoidingView>
    </>
  );
};

export default LoginScreen;
