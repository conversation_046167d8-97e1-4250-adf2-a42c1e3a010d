import {RootStackParamList} from '../../navigation/AppNavigator';
import React, {useState, useEffect} from 'react';
import HeaderView from '../../components/HeaderView/index';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {VenueService} from '../../services/api/APIServices';
import {Alert, FlatList, Text, StyleSheet, View} from 'react-native';
import {BarDetails} from '../../types/BarDetails';
import StorageHelper from '../../utils/storageHelper';
import VenueCardView from '../../components/VenueCardView/index';
import Colors from '../../utils/colors';
import CustomSafeAreaView from '../../components/CustomSafeAreaView/index';
import {CustomLoaderView} from '../../components/CustomLoaderView';
import {FlatListView} from '../../components/FlatListView';
import styles from '../../styles/commonStyles';

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen: React.FC<Props> = ({navigation}) => {
  const [arrBarList, setArrBarList] = useState<BarDetails[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const barlistWSCall = async () => {
    setIsRefreshing(true);
    try {
      const response = await VenueService.getBarList({
        latitude: '22.9857', //'-33.647778',
        longitude: '72.6432', //'115.345833',
        search: '',
        showAll: 1,
        page: 1,
        serviceType: 'BOTH',
        distanceKm: 50,
      });
      if (response.success && response.data) {
        setArrBarList(response.data.barlist);
        StorageHelper.saveArray('barList', response.data.barlist);
      }
    } catch (error) {
      console.log('response', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    setIsRefreshing(true);
    StorageHelper.getArray('barList').then(result => {
      if (result.success && result.data.length > 0) {
        setArrBarList(result.data);
        console.log('barlist', result.data);
      } else {
        barlistWSCall();
      }
      setIsRefreshing(false);
    });
  }, []);

  const handleRefresh = () => {
    barlistWSCall();
  };

  return (
    <CustomSafeAreaView style={styles.customSafeAreaViewStyle}>
      <View style={styles.container}>
        <HeaderView />
        <FlatListView
          arrData={arrBarList}
          renderItem={({item}) => (
            <VenueCardView
              item={item}
              onPress={() => {
                Alert.alert('Venue Details', item.restaurantName);
              }}
            />
          )}
          keyExtractor={item => item.id.toString()}
          emptyDataMessage="No venues available."
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
        />
        {isRefreshing && <CustomLoaderView isRefreshing={isRefreshing} />}
      </View>
    </CustomSafeAreaView>
  );
};

export default HomeScreen;
