import React, {useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '../navigation/AppNavigator';
import HeaderView from '../components/HeaderView/index';
import {AssetImages} from '../utils/constants';
import {TopSafeAreaView} from '../components/CustomSafeAreaView/index';
import GradientView from '../components/GradientView/index';
import TitleDescriptionView from '../components/TitleDescriptionView/index';
import {StaticContents, Placeholders, ButtonTitle} from '../utils/constants';
import ContainerInputFieldsView from '../components/Login/LoginInputFieldsView/index';
import InputField from '../components/InputField/index';
import TextButton from '../components/TextButton/index';
import TextButtonStyle from '../components/TextButton/styles';
import {AlertMessages, AlertTitles} from '../utils/constants';
import {validateEmail} from '../utils/validation';
import {AuthService} from '../services/api';
import {Alert} from 'react-native';
import {handleApiError} from '../utils/apiUtils';

type Props = NativeStackScreenProps<RootStackParamList, 'ForgotPassword'>;

const ForgotPasswordScreen: React.FC<Props> = ({navigation}) => {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');

  // Validation and API call
  const handleForgotPassword = async () => {
    if (!email.trim()) {
      setEmailError(AlertMessages.error.emailRequired);
      return;
    }
    if (!validateEmail(email)) {
      setEmailError(AlertMessages.error.invalidEmail);
      return;
    }
    await forgotPasswordWSCall();
  };

  // Implement forgot password API call
  const forgotPasswordWSCall = async () => {
    try {
      //Call API to reset password
      const response = await AuthService.forgotPassword(email.trim());
      console.log('response', response.success);
      if (response.success && response.data) {
        navigation.navigate('OtpVerification', {email});
      } else {
        Alert.alert(AlertTitles.error, response.message);
      }
    } catch (error) {
      // Handle API error
      Alert.alert(AlertTitles.error, handleApiError(error));
    }
  };

  return (
    <>
      <TopSafeAreaView />
      <HeaderView
        leftIcon={AssetImages.ic_back_icon}
        onPress={() => navigation.goBack()}
      />
      <GradientView>
        <TitleDescriptionView
          heading={StaticContents.ForgotPasswordScreen.forgotPassTitle}
          subheading={StaticContents.ForgotPasswordScreen.forgotPassSubtitle}
        />
        <ContainerInputFieldsView>
          <InputField
            placeholder={Placeholders.email}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            error={emailError ? {message: emailError} : null}></InputField>
          <TextButton
            text={ButtonTitle.continue}
            onPress={() => handleForgotPassword()}
            textButtonStyle={TextButtonStyle.btnStyle}
          />
        </ContainerInputFieldsView>
      </GradientView>
    </>
  );
};

export default ForgotPasswordScreen;
