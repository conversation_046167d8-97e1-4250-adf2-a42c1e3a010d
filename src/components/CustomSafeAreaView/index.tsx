import React from 'react';
import {SafeAreaView as RNSafeAreaView, ViewStyle} from 'react-native';
import {
  SafeAreaView as SafeAreaViewContext,
  SafeAreaViewProps,
  Edge,
} from 'react-native-safe-area-context';
import Colors from '../../utils/colors';

interface CustomSafeAreaViewProps extends SafeAreaViewProps {
  color?: string;
}

/**
 * A custom SafeAreaView component that applies a consistent background color
 * to the safe area. This component should be used at the top of each screen.
 */
const CustomSafeAreaView: React.FC<CustomSafeAreaViewProps> = ({
  color = Colors.app_gradient[0],
  style,
  ...props
}) => {
  return (
    <SafeAreaViewContext style={[{backgroundColor: color}, style]} {...props} />
  );
};

/**
 * A simplified version of CustomSafeAreaView that only renders the top edge.
 * Use this when you only need to color the top safe area.
 */
export const TopSafeAreaView: React.FC<CustomSafeAreaViewProps> = ({
  color = Colors.app_white,
  style,
  ...props
}) => {
  // Restrict edges to only top
  const edges: Edge[] = ['top'];
  return (
    <SafeAreaViewContext
      edges={edges}
      style={[{backgroundColor: color}, style]}
      {...props}
    />
  );
};

/**
 * A simplified version of CustomSafeAreaView that only renders the bottom edge.
 * Use this when you only need to color the bottom safe area.
 */
export const BottomSafeAreaView: React.FC<CustomSafeAreaViewProps> = ({
  color = Colors.app_white,
  style,
  ...props
}) => {
  // Restrict edges to only bottom
  const edges: Edge[] = ['bottom'];
  return (
    <SafeAreaViewContext
      edges={edges}
      style={[{backgroundColor: color}, style]}
      {...props}
    />
  );
};

export default CustomSafeAreaView;
