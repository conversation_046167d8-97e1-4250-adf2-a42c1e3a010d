import {Text, TextStyle, TouchableOpacity} from 'react-native';

type TextButtonPrompt = {
  text: string;
  onPress: () => void;
  textButtonStyle: TextStyle | TextStyle[];
};

const TextButton: React.FC<TextButtonPrompt> = ({
  text,
  onPress,
  textButtonStyle,
}) => {
  return (
    <TouchableOpacity
      onPress={() => {
        onPress();
      }}>
      <Text style={textButtonStyle}> {text} </Text>
    </TouchableOpacity>
  );
};

export default TextButton;
