import {StyleSheet, TextStyle} from 'react-native';
import {FontFamily, FontSize} from '../../utils/fonts';
import Colors from '../../utils/colors';

const TextButtonStyle = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 10,
  },

  forgotPassBtnStyle: {
    fontFamily: FontFamily.Balloobhai2_MEDIUM,
    fontSize: FontSize.size_17,
    color: Colors.app_orange,
  },

  btnStyle: {
    fontFamily: FontFamily.Balloobhai2_SEMIBOLD,
    fontSize: FontSize.size_18,
    backgroundColor: Colors.app_white,
    color: Colors.app_orange,
    borderRadius: 16,
    height: 50,
    width: '100%',
    marginTop: 20,
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
});

export default TextButtonStyle;
