import {ActivityIndicator, View, Modal} from 'react-native';
import {styles} from './styles';
import Colors from '../../utils/colors.ts';

type LoaderViewProps = {
  isRefreshing: boolean;
};

export const CustomLoaderView: React.FC<LoaderViewProps> = ({isRefreshing}) => {
  return (
    <Modal transparent={true} visible={isRefreshing}>
      <View style={styles.overlay}>
        <ActivityIndicator size="large" color={Colors.app_orange} />
      </View>
    </Modal>
  );
};
