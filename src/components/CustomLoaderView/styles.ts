import {StyleSheet} from 'react-native';
import Colors from '../../utils/colors';

export const styles = StyleSheet.create({
  overlay: {
    position: 'absolute', // Keeps the overlay above all other content
    top: '50%', // Center vertically
    left: '50%', // Center horizontally
    transform: [{translateX: -50}, {translateY: -50}], // Adjust to exactly center it
    width: 100, // Fixed width
    height: 100, // Fixed height
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.app_white, // Slight transparency for overlay
    zIndex: 1, // Ensures the overlay appears above other components
    shadowColor: Colors.app_black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
    borderRadius: 8,
  },
});
