import React, {useState} from 'react';
import {
  TextInput,
  TextInputProps,
  TouchableOpacity,
  View,
  Text,
} from 'react-native';
import inputFieldStyles from './styles';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Colors from '../../utils/colors';
import {Icons} from '../../utils/constants';

interface inputErrorPrompt {
  message: string;
  color?: string;
}

interface InputTextFieldPrompt extends TextInputProps {
  error: inputErrorPrompt | null;
}

const InputField: React.FC<InputTextFieldPrompt> = ({
  placeholder,
  value,
  onChangeText,
  keyboardType = 'default',
  secureTextEntry,
  error,
}) => {
  const [secure, setSecure] = useState(secureTextEntry);

  return (
    <>
      <View
        style={[
          inputFieldStyles.container,
          error?.message ? inputFieldStyles.containerError : null,
        ]}>
        <TextInput
          style={[inputFieldStyles.input]}
          placeholder={placeholder}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={secure}
          keyboardType={keyboardType}
          placeholderTextColor={Colors.app_black_50}
        />
        {secureTextEntry === true && (
          <TouchableOpacity
            onPress={() => setSecure(!secure)}
            style={inputFieldStyles.icon}>
            <Ionicons
              name={secure ? Icons.eye_off : Icons.eye}
              size={24}
              color={Colors.app_black_50}
            />
          </TouchableOpacity>
        )}
      </View>
      {error?.message ? (
        <Text
          style={[inputFieldStyles.errorText, {color: error.color || 'red'}]}>
          {error.message}
        </Text>
      ) : null}
    </>
  );
};

export default InputField;
