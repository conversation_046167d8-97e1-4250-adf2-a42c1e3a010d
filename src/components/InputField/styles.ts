import {StyleSheet} from 'react-native';
import Colors from '../../utils/colors';
import {FontFamily, FontSize} from '../../utils/fonts';

const inputFieldStyles = StyleSheet.create({
  input: {
    flex: 1,
    height: 45,
    fontFamily: FontFamily.Balloobhai2_REGULAR,
    color: Colors.app_black,
    fontSize: FontSize.size_16,
  },
  icon: {
    paddingHorizontal: 6,
    paddingVertical: 4,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.app_white,
    borderRadius: 10,
    paddingHorizontal: 10,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: Colors.app_white,
  },
  containerError: {
    borderColor: 'red',
  },
  errorText: {
    marginLeft: 10,
    fontSize: FontSize.size_14,
    fontFamily: FontFamily.Balloobhai2_REGULAR,
  },
});

export default inputFieldStyles;
