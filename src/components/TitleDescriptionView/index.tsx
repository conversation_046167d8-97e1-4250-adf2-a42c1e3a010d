import React from 'react';
import {View, Text, TextStyle} from 'react-native';
import titleDescriptionStyles from './styles';
import {FontSize} from '../../utils/fonts';

interface TitleDescriptionPrompt {
  heading: string;
  subheading: string;
}

const TitleDescriptionView: React.FC<TitleDescriptionPrompt> = ({
  heading,
  subheading,
}) => {
  return (
    <View style={titleDescriptionStyles.container}>
      <Text
        style={[
          {fontSize: FontSize.size_24},
          titleDescriptionStyles.headingStyle,
        ]}>
        {heading}
      </Text>
      <Text
        style={[
          {fontSize: FontSize.size_17},
          titleDescriptionStyles.subheadingStyle,
        ]}>
        {subheading}
      </Text>
    </View>
  );
};

export default TitleDescriptionView;
