import React, {ReactNode, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import {inputFieldContainerStyle} from './styles';

type ContainerInputFieldsViewProps = {
  children?: React.ReactNode;
};

const ContainerInputFieldsView: React.FC<ContainerInputFieldsViewProps> = ({
  children,
}) => {
  return (
    <View style={inputFieldContainerStyle.inputFieldContainer}>{children}</View>
  );
};

export default ContainerInputFieldsView;
