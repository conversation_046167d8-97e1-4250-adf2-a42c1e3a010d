import {View, Text, TouchableOpacity} from 'react-native';
import NoAccountViewStyle from './styles';
import {StaticContents, ButtonTitle} from '../../../utils/constants';
import TextButton from '../../TextButton/index';
import TextButtonStyle from '../../TextButton/styles';

type NoAccountProps = {
  noMoreAccountFontFamily: string;
  signUpFontfamily: string;
  onClick: () => void;
};

const NoAccountView: React.FC<NoAccountProps> = ({
  onClick,
  noMoreAccountFontFamily,
  signUpFontfamily,
}) => {
  return (
    <View style={NoAccountViewStyle.container}>
      <Text
        style={[
          {fontFamily: noMoreAccountFontFamily},
          NoAccountViewStyle.textStyle,
        ]}>
        {' '}
        {StaticContents.loginScreen.noAccount}{' '}
      </Text>
      <TouchableOpacity onPress={onClick}>
        <Text
          style={[
            {fontFamily: signUpFontfamily},
            NoAccountViewStyle.textStyle,
          ]}>
          {' '}
          {ButtonTitle.signup}{' '}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default NoAccountView;
