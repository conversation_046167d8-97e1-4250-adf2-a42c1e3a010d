import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import Colors from '../../utils/colors';
import gradientViewStyles from './styles';

type GradientViewProps = {
  children?: React.ReactNode;
};

const GradientView: React.FC<GradientViewProps> = ({children}) => {
  return (
    <LinearGradient
      colors={Colors.app_gradient}
      style={gradientViewStyles.gradient}>
      {children}
    </LinearGradient>
  );
};
export default GradientView;
