import {
  FlatList,
  Text,
  StyleSheet,
  View,
  ListRenderItemInfo,
} from 'react-native';
import React from 'react';
import {styles} from './styles';

type FlatListViewProps = {
  arrData: any[];
  renderItem: (info: ListRenderItemInfo<any>) => React.ReactElement | null;
  keyExtractor: (item: any) => string;
  emptyDataMessage?: string;
  style?: any;
  refreshing?: boolean;
  onRefresh?: () => void;
};

export const FlatListView: React.FC<FlatListViewProps> = ({
  arrData,
  renderItem,
  keyExtractor,
  emptyDataMessage,
  style,
  refreshing,
  onRefresh,
}) => {
  return (
    <FlatList
      data={arrData}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListEmptyComponent={() => (
        <Text style={styles.ListEmptyComponentStyle}>{emptyDataMessage}</Text>
      )}
      style={style ? style : styles.FlatListStyle}
      refreshing={refreshing}
      onRefresh={onRefresh}
    />
  );
};
