import React from 'react';
import {View, Image} from 'react-native';
import headerViewStyles from './styles';
import {TouchableOpacity} from 'react-native';
import {AssetImages} from '../../utils/constants';

interface HeaderViewProps {
  onPress?: () => void;
  leftIcon?: any;
}

const HeaderView: React.FC<HeaderViewProps> = ({onPress, leftIcon}) => {
  return (
    <View style={headerViewStyles.headerViewStyle}>
      {leftIcon ? (
        <TouchableOpacity onPress={onPress}>
          <Image
            source={leftIcon || AssetImages.ic_back_icon}
            style={headerViewStyles.leftButtonStyle}
            resizeMode="center"
          />
        </TouchableOpacity>
      ) : null}
      <Image
        source={AssetImages.ic_header_logo}
        style={headerViewStyles.headerImageStyle}
        resizeMode="center"
      />
      {leftIcon ? <View style={headerViewStyles.rightSpacerStyle} /> : null}
    </View>
  );
};
export default HeaderView;
