import {View, Image, Text, TouchableOpacity} from 'react-native';
import {BarDetails} from '../../types/BarDetails';
import React from 'react';
import {VenueCardViewStyles} from './styles';
import {AssetImages} from '../../utils/constants';

interface VenueCardViewProps {
  item: BarDetails;
  onPress: () => void;
}

const VenueCardView: React.FC<VenueCardViewProps> = ({item, onPress}) => {
  return (
    <TouchableOpacity onPress={onPress}>
      <View style={VenueCardViewStyles.cardStyle}>
        <Image
          style={VenueCardViewStyles.imageStyle}
          source={{uri: item.avatar}}
        />
        {/* Main text container */}
        <View style={VenueCardViewStyles.mainTextContainerStyle}>
          {/* Left side text container */}
          <View style={VenueCardViewStyles.leftTextContainerStyle}>
            <Text style={VenueCardViewStyles.barTitleStyle}>
              {item.restaurantName}
            </Text>
            <Text style={VenueCardViewStyles.serviceTypeStyle}>
              {item.serviceType}
            </Text>
            <View style={VenueCardViewStyles.totalOrderContainerStyle}>
              <Image
                style={VenueCardViewStyles.smileyImageIconStyle}
                source={AssetImages.ic_total_order_smiley}
              />
              <Text style={VenueCardViewStyles.totalOrdersStyle}>
                {`${item.totalOrders} orders`}
              </Text>
            </View>
          </View>
          {/* Right side text container */}
          <View style={VenueCardViewStyles.rightTextContainerStyle}>
            <Image
              style={VenueCardViewStyles.directionImageIconStyle}
              source={AssetImages.ic_direction}
            />
            <Text style={VenueCardViewStyles.distanceStyle}>
              {`${item.distance} ${item.distance_ext}`}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default VenueCardView;
