import {StyleSheet} from 'react-native';
import Colors from '../../utils/colors';
import {FontSize} from '../../utils/fonts';

export const VenueCardViewStyles = StyleSheet.create({
  cardStyle: {
    backgroundColor: Colors.app_white,

    // iOS shadow properties
    shadowColor: Colors.app_black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3, // <-- add shadowRadius for blur

    // Android shadow
    elevation: 10,
    marginBottom: 16,
  },

  imageStyle: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
    alignContent: 'center',
  },

  leftTextContainerStyle: {
    paddingLeft: 10,
    flexDirection: 'column',
  },

  rightTextContainerStyle: {
    paddingRight: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },

  mainTextContainerStyle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  barTitleStyle: {
    fontFamily: 'BalooBhai2-Medium',
    color: Colors.app_black,
    fontSize: FontSize.size_20,
    textAlign: 'left',
  },

  serviceTypeStyle: {
    fontFamily: 'BalooBhai2-Regular',
    color: Colors.app_black_50,
    fontSize: FontSize.size_17,
    textAlign: 'left',
  },

  totalOrderContainerStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  totalOrdersStyle: {
    fontFamily: 'BalooBhai2-Regular',
    color: Colors.app_black,
    fontSize: FontSize.size_15,
    textAlign: 'left',
  },

  smileyImageIconStyle: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 5,
  },

  distanceStyle: {
    fontFamily: 'BalooBhai2-Regular',
    color: Colors.app_black,
    fontSize: FontSize.size_16,
    textAlign: 'right',
  },

  directionImageIconStyle: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 5,
    marginBottom: 4,
  },
});
