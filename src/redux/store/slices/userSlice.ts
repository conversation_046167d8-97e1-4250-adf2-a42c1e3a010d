import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {UserProfile} from '../../../services/models';

const initialState: UserProfile = {
  avatar: '',
  id: 0,
  fullName: '',
  email: '',
  birthday: '',
  countryCode: '',
  mobile: '',
  mobileVerified: '',
  badge: 0,
  notification: '',
  status: '',
  accessToken: '',
  cartItemAvailable: '',
  barName: '',
  loginType: '',
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    loginUser: (state, action: PayloadAction<UserProfile>) => {
      console.log('🚀 ~ action.payload:', action.payload);
      Object.assign(state, action.payload);
    },
    logoutUser: () => initialState,
  },
});
export const {loginUser, logoutUser} = userSlice.actions;
export default userSlice.reducer;
