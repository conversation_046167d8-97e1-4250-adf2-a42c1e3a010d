import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {BarDetails} from '../../../types/BarDetails';

const initalState: BarDetails = {
  id: 0,
  name: '',
  address: '',
  city: '',
  state: '',
  zip: '',
  country: '',
  phone: '',
  email: '',
  website: '',
  description: '',
  image: '',
};

const venueSlice = createSlice({
  name: 'venue',
  initialState: initalState,
  reducers: {
    getBarList: (state, action: PayloadAction<BarDetails>) => {
      console.log('🚀 ~ action.payload:', action.payload);
      Object.assign(state, action.payload);
    },
  },
});

export const {getBarList} = venueSlice.actions;
export default venueSlice.reducer;
