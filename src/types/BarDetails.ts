import {UserProfile} from './User';

export interface BarList {
  barlist: BarDetails[];
  cartData: any;
  userData: UserProfile;
  count: number;
}

export interface BarDetails {
  address: string;
  avatar: string;
  countryCode: string;
  distance: number;
  distance_ext: string;
  email: string;
  id: number;
  isOpen: number;
  isVenueFavorite: number;
  isVenueServeAlcohol: string | null;
  latitude: string;
  liquorLicenseNumber: string | null;
  longitude: string;
  managerName: string;
  mobile: string;
  operatingFlag: number;
  operating_hours: Array<any>; // Replace 'any' with a more specific type if available
  restaurantName: string;
  serviceType: string;
  totalOrders: number;
  waitTimeDrink: number;
  waitTimeFood: number;
}
