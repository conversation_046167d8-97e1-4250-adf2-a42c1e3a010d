/**
 * API utility functions
 */

import {NetworkError} from '../services/network/types';
import {ApiResponse} from '../services/models/Response';

/**
 * Format error message from API response
 */
export const formatErrorMessage = (error: NetworkError): string => {
  if (error.data && typeof error.data === 'object') {
    // Check if it's a structured API error response
    const apiError = error.data as any;

    if (apiError.message) {
      return apiError.message;
    }

    if (apiError.errors) {
      // Flatten error messages from validation errors
      return Object.values(apiError.errors).flat().join(', ');
    }
  }

  // Default error message
  return error.message || 'An unknown error occurred';
};

/**
 * Create a successful API response
 */
export const createSuccessResponse = <T>(
  data: T,
  message: string = 'Success',
): ApiResponse<T> => {
  return {
    success: true,
    data,
    message,
  };
};

/**
 * Create an error API response
 */
export const createErrorResponse = <T>(
  message: string,
  errors?: Record<string, string[]>,
): ApiResponse<T> => {
  return {
    success: false,
    data: null,
    message,
    errors,
  };
};

/**
 * Handle API errors in a consistent way
 */
export const handleApiError = (error: any): string => {
  if (error.data && error.data.message) {
    return error.data.message;
  }

  if (error.status === 401) {
    return 'Your session has expired. Please login again.';
  }

  if (error.status === 403) {
    return 'You do not have permission to perform this action.';
  }

  if (error.status === 404) {
    return 'The requested resource was not found.';
  }

  if (error.status >= 500) {
    return 'A server error occurred. Please try again later.';
  }

  return error.message || 'An unknown error occurred';
};
