import AsyncStorage from '@react-native-async-storage/async-storage';

class StorageHelper {
  // Save array
  static async saveArray(key, data) {
    try {
      if (!Array.isArray(data)) {
        throw new Error('Data must be an array');
      }
      const jsonString = JSON.stringify(data);
      await AsyncStorage.setItem(key, jsonString);
      return {success: true};
    } catch (error) {
      console.error('Error saving array:', error);
      return {success: false, error: error.message};
    }
  }

  // Get array
  static async getArray(key) {
    try {
      const jsonString = await AsyncStorage.getItem(key);
      if (jsonString) {
        const data = JSON.parse(jsonString);
        return {success: true, data};
      }
      return {success: true, data: []};
    } catch (error) {
      console.error('Error retrieving array:', error);
      return {success: false, error: error.message, data: []};
    }
  }

  // Append to existing array
  static async appendToArray(key, newItem) {
    try {
      const result = await this.getArray(key);
      if (result.success) {
        const updatedArray = [...result.data, newItem];
        return await this.saveArray(key, updatedArray);
      }
      return result;
    } catch (error) {
      console.error('Error appending to array:', error);
      return {success: false, error: error.message};
    }
  }
}
export default StorageHelper;
